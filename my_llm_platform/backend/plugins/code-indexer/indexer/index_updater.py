# ==============================================================
"""スキャン → 変化差分抽出 → Faiss 反映までを司る高水準クラス。"""

from __future__ import annotations
import json
import hashlib
from pathlib import Path
from typing import Iterable

import faiss  # type: ignore[import]
import numpy as np

from .file_scanner import DirectoryScanner
from .chunker import CodeChunker
from .embedder import Embedder
from .watcher import start_watcher
from .merkle import build_merkle

FAISS_DIM_PLACEHOLDER = 768  # モデル次第で自動取得可
STATE_FILE = ".index_state.json"

# チャンク置換機能の設定
REBUILD_THRESHOLD = 100000  # 削除されたベクトル数がこの値を超えたら完全再構築
MAX_CHUNK_ID = 2**63 - 1    # 64bit整数の最大値


class CodeIndexer:
    """コードベース向けインデクサ。"""

    def __init__(self, repo_path: str | Path, index_path: str | Path):
        self.repo_path = Path(repo_path).resolve()
        self.index_path = Path(index_path).resolve()
        self.scanner = DirectoryScanner(self.repo_path)
        self.chunker = CodeChunker()
        self.embedder = Embedder()
        self.faiss_index = self._init_faiss()
        self._state = self._load_state()
        self._deleted_count = self._state.get("deleted_count", 0)  # 削除されたベクトル数を追跡

    # ────────────────────────────────────────
    # パブリック API
    # ────────────────────────────────────────
    def full_build(self):
        """全ファイルを再インデックス。IndexIDMapを使用して削除機能を有効化。"""
        # 新しいIndexIDMapインデックスを作成（互換性のため）
        dim = self._get_embedding_dimension()
        base_index = faiss.IndexFlatL2(dim)
        # IndexIDMap2が利用できない場合はIndexIDMapを使用
        try:
            self.faiss_index = faiss.IndexIDMap2(base_index)
        except AttributeError:
            # フォールバック: IndexIDMapを使用
            self.faiss_index = faiss.IndexIDMap(base_index)

        file_hashes = self.scanner.scan()
        self._state["merkle_root"] = build_merkle(list(file_hashes.values()))
        self._state["hashes"] = file_hashes
        # 完全再構築の場合はid_mapをクリア
        self._state["id_map"] = {}
        self._deleted_count = 0
        self._state["deleted_count"] = 0
        self._reindex_files(file_hashes.keys())
        self._save_state()

    def rebuild_index(self):
        """インデックスを完全に再構築。削除されたベクトルを除去してIVF圧縮効率を改善。"""
        print("インデックスの完全再構築を開始...")

        # 新しいインデックスを作成
        dim = self._get_embedding_dimension()
        base_index = faiss.IndexFlatL2(dim)
        new_index = faiss.IndexIDMap2(base_index)

        # 既存のファイルを全て再インデックス
        file_hashes = self.scanner.scan()
        self._state["merkle_root"] = build_merkle(list(file_hashes.values()))
        self._state["hashes"] = file_hashes
        self._state["id_map"] = {}

        # 古いインデックスを新しいものに置き換え
        self.faiss_index = new_index
        self._deleted_count = 0
        self._state["deleted_count"] = 0

        # 全ファイルを再インデックス
        self._reindex_files(file_hashes.keys())
        self._save_state()

        print(f"インデックスの再構築が完了しました。{len(file_hashes)} ファイルを処理しました。")

    def get_index_stats(self) -> dict:
        """インデックスの統計情報を取得。"""
        total_vectors = self.faiss_index.ntotal
        total_files = len(self._state["hashes"])
        total_chunks = sum(len(chunk_ids) for chunk_ids in self._state["id_map"].values())
        deleted_count = self._deleted_count

        return {
            "total_vectors": total_vectors,
            "total_files": total_files,
            "total_chunks": total_chunks,
            "deleted_count": deleted_count,
            "rebuild_recommended": deleted_count > REBUILD_THRESHOLD,
            "index_dimension": self.faiss_index.d if hasattr(self.faiss_index, 'd') else "unknown"
        }

    def watch(self):
        """ファイル変更を監視し、自動更新する。"""
        start_watcher(
            self.repo_path,
            callback=self._handle_change,
            ignore_patterns=["*.log", "*.tmp", ".git/*"],
        )

    # ────────────────────────────────────────
    # 内部処理
    # ────────────────────────────────────────
    def _init_faiss(self):
        """Faissインデックスを初期化。IndexIDMapでラップして削除機能を有効化。"""
        if self.index_path.exists():
            try:
                # 既存のインデックスを読み込み
                index = faiss.read_index(str(self.index_path))

                # IndexIDMapまたはIndexIDMap2でラップされているかチェック
                if isinstance(index, (faiss.IndexIDMap, faiss.IndexIDMap2)):
                    # 既にIDMapでラップされている場合はそのまま使用
                    return index
                else:
                    print("⚠️  古いインデックス形式を検出。IndexIDMapに変換中...")
                    # 既存のベクトルを取得してIDMapで再構築
                    if index.ntotal > 0:
                        try:
                            # 既存のベクトルを取得
                            vectors = np.zeros((index.ntotal, index.d), dtype=np.float32)
                            for i in range(index.ntotal):
                                vectors[i] = index.reconstruct(i)

                            # 新しいIndexIDMapを作成
                            base_index = faiss.IndexFlatL2(index.d)
                            try:
                                id_map_index = faiss.IndexIDMap2(base_index)
                            except AttributeError:
                                id_map_index = faiss.IndexIDMap(base_index)

                            # 既存のベクトルを連続IDで追加
                            ids = np.arange(index.ntotal, dtype=np.int64)
                            id_map_index.add_with_ids(vectors, ids)
                            print("✅ インデックス変換完了")
                            return id_map_index
                        except Exception as e:
                            print(f"⚠️  インデックス変換に失敗: {e}")
                            print("🔄 新しいインデックスを作成します...")
                            # 変換に失敗した場合は新しいインデックスを作成
                            return self._create_new_index()
                    else:
                        # 空のインデックスの場合は新しく作成
                        return self._create_new_index()

            except Exception as e:
                print(f"⚠️  既存インデックスの読み込みに失敗: {e}")
                print("🔄 新しいインデックスを作成します...")
                # 読み込みに失敗した場合は新しいインデックスを作成
                return self._create_new_index()
        else:
            # 新しいインデックスを作成
            return self._create_new_index()

    def _create_new_index(self):
        """新しいIndexIDMapインデックスを作成。"""
        dim = self._get_embedding_dimension()
        base_index = faiss.IndexFlatL2(dim)
        try:
            return faiss.IndexIDMap2(base_index)
        except AttributeError:
            return faiss.IndexIDMap(base_index)

    def _get_embedding_dimension(self) -> int:
        """埋め込みモデルの次元数を取得。"""
        try:
            # テスト用の短いテキストで次元数を確認
            test_chunks = ["test"]
            test_vector = self.embedder.embed(test_chunks)
            return test_vector.shape[1]
        except Exception:
            # フォールバック
            return FAISS_DIM_PLACEHOLDER

    def _save_index(self):
        faiss.write_index(self.faiss_index, str(self.index_path))

    def _load_state(self):
        p = self.index_path.with_suffix(".json")
        if p.exists():
            state = json.loads(p.read_text("utf-8"))
            # 新しいフィールドが存在しない場合は初期化
            if "id_map" not in state:
                state["id_map"] = {}
            if "deleted_count" not in state:
                state["deleted_count"] = 0
            return state
        return {
            "hashes": {},
            "merkle_root": "",
            "id_map": {},  # {filepath: [id1, id2, ...]}
            "deleted_count": 0
        }

    def _save_state(self):
        # 削除カウントを状態に保存
        self._state["deleted_count"] = self._deleted_count
        p = self.index_path.with_suffix(".json")
        p.write_text(json.dumps(self._state, ensure_ascii=False, indent=2))
        self._save_index()

    def _generate_chunk_id(self, file_path: str, chunk_no: int) -> int:
        """ファイルパスとチャンク番号から決定的なIDを生成。"""
        # SHA256ハッシュを使用して決定的なIDを生成
        content = f"{file_path}#{chunk_no}"
        hash_bytes = hashlib.sha256(content.encode('utf-8')).digest()
        # 最初の8バイトを64bit整数に変換
        chunk_id = int.from_bytes(hash_bytes[:8], byteorder='big', signed=False)
        # 最大値を超えないように調整
        return chunk_id % MAX_CHUNK_ID

    # ファイル変更コールバック
    def _handle_change(self, changed_path: str):
        """ファイル変更を処理。削除、変更、追加に対応。"""
        rel = Path(changed_path).resolve()
        if rel.is_dir():
            return  # ディレクトリ自体は無視

        file_path_str = str(rel)
        file_name = rel.name

        # ファイルが削除された場合
        if not rel.exists():
            print(f"🗑️  ファイル削除処理: {file_name}")
            self._remove_file_from_index(file_path_str)
            print(f"✅ ファイル削除完了: {file_name}")
            return

        # ファイルがフィルタリング対象かチェック
        if not self.scanner._is_code_file(rel):
            print(f"🚫 ファイルスキップ（除外対象）: {file_name}")
            return

        try:
            new_hash = self.scanner._file_hash(rel)
            old_hash = self._state["hashes"].get(file_path_str)

            if new_hash == old_hash:
                print(f"⏭️  ファイルスキップ（変更なし）: {file_name}")
                return  # 実質変化なし

            # ファイルが変更された場合、古いベクトルを削除してから新しいものを追加
            if old_hash:
                print(f"🔄 ファイル更新処理: {file_name}")
            else:
                print(f"➕ ファイル新規追加: {file_name}")

            print(f"   📊 ハッシュ: {old_hash[:8] if old_hash else 'なし'}... → {new_hash[:8]}...")

            self._state["hashes"][file_path_str] = new_hash
            self._reindex_files([rel])
            self._save_state()

            print(f"✅ ファイル処理完了: {file_name}")

        except Exception as e:
            print(f"❌ ファイル {file_name} の処理中にエラーが発生: {e}")
            import traceback
            traceback.print_exc()

    def _remove_file_from_index(self, file_path: str):
        """インデックスからファイルのチャンクを削除。"""
        if file_path in self._state["id_map"]:
            chunk_ids = self._state["id_map"][file_path]
            if chunk_ids:
                # Faissから古いベクトルを削除
                ids_array = np.array(chunk_ids, dtype=np.int64)
                self.faiss_index.remove_ids(ids_array)
                self._deleted_count += len(chunk_ids)

            # 状態から削除
            del self._state["id_map"][file_path]

        if file_path in self._state["hashes"]:
            del self._state["hashes"][file_path]

        self._save_state()

    def _reindex_files(self, files: Iterable[str | Path]):
        """ファイルを再インデックス。古いチャンクを削除してから新しいチャンクを追加。"""
        for fp in files:
            file_path_str = str(Path(fp).resolve())

            try:
                # 古いチャンクを削除
                if file_path_str in self._state["id_map"]:
                    old_chunk_ids = self._state["id_map"][file_path_str]
                    if old_chunk_ids:
                        ids_array = np.array(old_chunk_ids, dtype=np.int64)
                        self.faiss_index.remove_ids(ids_array)
                        self._deleted_count += len(old_chunk_ids)

                # ファイルを読み込んでチャンクに分割
                text = Path(fp).read_text(encoding="utf-8", errors="ignore")
                chunks = self.chunker.split(text, file_path_str)

                if not chunks:
                    # チャンクが空の場合はid_mapから削除
                    if file_path_str in self._state["id_map"]:
                        del self._state["id_map"][file_path_str]
                    continue

                # 新しいチャンクIDを生成
                new_chunk_ids = [
                    self._generate_chunk_id(file_path_str, i)
                    for i in range(len(chunks))
                ]

                # 埋め込みベクトルを生成
                vectors = self.embedder.embed(chunks)

                # FaissにIDと一緒に追加
                ids_array = np.array(new_chunk_ids, dtype=np.int64)
                self.faiss_index.add_with_ids(vectors, ids_array)

                # 状態を更新
                self._state["id_map"][file_path_str] = new_chunk_ids

                # 閾値チェック：削除されたベクトル数が多い場合は再構築を検討
                if self._deleted_count > REBUILD_THRESHOLD:
                    print(f"削除されたベクトル数が {self._deleted_count} に達しました。")
                    print("インデックスの再構築を推奨します。rebuild_index() メソッドを呼び出してください。")

            except Exception as e:
                print(f"ファイル {file_path_str} の再インデックス中にエラーが発生: {e}")
                continue


