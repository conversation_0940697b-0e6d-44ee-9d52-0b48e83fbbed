# ==============================================================
"""ファイル変更検知 (watchdog)."""

from __future__ import annotations
import time
from pathlib import Path
from typing import Callable, List

from watchdog.events import PatternMatchingEventHandler  # type: ignore[import]
from watchdog.observers import Observer  # type: ignore[import]


class CodeChangeHandler(PatternMatchingEventHandler):
    """任意イベントでコールバックを呼び出す。"""

    def __init__(
        self, callback: Callable[[str], None], ignore_patterns: List[str] | None = None
    ) -> None:
        super().__init__(ignore_patterns=ignore_patterns or [])
        self.callback = callback

    # すべてのイベントで同じコールバック
    on_modified = on_created = on_moved = on_deleted = lambda self, event: self.callback(
        event.src_path
    )


def start_watcher(path: str | Path, callback: Callable[[str], None], **kwargs):
    """監視を開始し、KeyboardInterrupt で停止。"""
    observer = Observer()
    observer.schedule(CodeChangeHandler(callback, **kwargs), str(path), recursive=True)
    observer.start()
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        observer.stop()
    observer.join()


