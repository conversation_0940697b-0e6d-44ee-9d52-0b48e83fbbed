# ==============================================================
"""ファイル変更検知 (watchdog)."""

from __future__ import annotations
import time
from pathlib import Path
from typing import Callable, List

from watchdog.events import PatternMatchingEventHandler  # type: ignore[import]
from watchdog.observers import Observer  # type: ignore[import]


class CodeChangeHandler(PatternMatchingEventHandler):
    """任意イベントでコールバックを呼び出す。詳細なログ出力付き。"""

    def __init__(
        self, callback: Callable[[str], None], ignore_patterns: List[str] | None = None
    ) -> None:
        super().__init__(ignore_patterns=ignore_patterns or [])
        self.callback = callback

    def on_modified(self, event):
        """ファイル変更時の処理"""
        if not event.is_directory:
            print(f"📝 ファイル変更検出: {event.src_path}")
            self.callback(event.src_path)

    def on_created(self, event):
        """ファイル作成時の処理"""
        if not event.is_directory:
            print(f"➕ ファイル作成検出: {event.src_path}")
            self.callback(event.src_path)

    def on_deleted(self, event):
        """ファイル削除時の処理"""
        if not event.is_directory:
            print(f"🗑️  ファイル削除検出: {event.src_path}")
            self.callback(event.src_path)

    def on_moved(self, event):
        """ファイル移動時の処理"""
        if not event.is_directory:
            print(f"📦 ファイル移動検出: {event.src_path} → {event.dest_path}")
            # 移動元を削除として処理
            self.callback(event.src_path)
            # 移動先を作成として処理
            self.callback(event.dest_path)


def start_watcher(path: str | Path, callback: Callable[[str], None], **kwargs):
    """監視を開始し、KeyboardInterrupt で停止。"""
    observer = Observer()
    observer.schedule(CodeChangeHandler(callback, **kwargs), str(path), recursive=True)
    observer.start()
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        observer.stop()
    observer.join()


