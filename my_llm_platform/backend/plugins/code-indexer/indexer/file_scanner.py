# ==============================================================
"""ディレクトリ走査 & .gitignore 対応ハッシュ計算。"""

from __future__ import annotations
import hashlib
import os
from pathlib import Path
from typing import Dict, Iterable, Set

import pathspec  # type: ignore[import]

DEFAULT_HASH_ALG = "sha256"
IGNORE_FILE_NAME = ".gitignore"


class DirectoryScanner:
    """対象ディレクトリを走査し、ファイルハッシュ辞書を返すクラス。"""

    def __init__(self, root: str | Path):
        self.root = Path(root).resolve()
        self.spec, self.gitignore_root = self._load_gitignore()

    # ────────────────────────────────────────
    # パブリック API
    # ────────────────────────────────────────
    def scan(self) -> Dict[str, str]:
        """root 配下ファイルを走査し {path: hash} を返却。"""
        result: Dict[str, str] = {}
        for f in self._iter_files():
            result[str(f)] = self._file_hash(f)
        return result

    # ────────────────────────────────────────
    # 内部処理
    # ────────────────────────────────────────
    def _load_gitignore(self):
        """プロジェクトルートの .gitignore ファイルを読み込む。

        Returns:
            tuple: (pathspec.PathSpec, gitignore_root_path)
        """
        # まず、スキャン対象ディレクトリ内の .gitignore をチェック
        ignore_path = self.root / IGNORE_FILE_NAME
        if ignore_path.exists():
            with ignore_path.open("r", encoding="utf-8") as fp:
                spec = pathspec.PathSpec.from_lines("gitwildmatch", fp)
            return spec, self.root

        # スキャン対象ディレクトリにない場合、親ディレクトリを遡って探す
        current_path = self.root.parent
        while current_path != current_path.parent:  # ルートディレクトリに到達するまで
            gitignore_path = current_path / IGNORE_FILE_NAME
            if gitignore_path.exists():
                with gitignore_path.open("r", encoding="utf-8") as fp:
                    spec = pathspec.PathSpec.from_lines("gitwildmatch", fp)
                return spec, current_path
            current_path = current_path.parent

        return pathspec.PathSpec([]), self.root

    def _iter_files(self) -> Iterable[Path]:
        """ファイルを再帰的に走査し、.gitignore ルールに従ってフィルタリング。

        ディレクトリレベルでの除外も適切に処理する。
        """
        for dirpath, dirnames, filenames in os.walk(self.root):
            # 現在のディレクトリの相対パス（gitignore ルートからの相対パス）
            current_dir_abs = Path(dirpath)

            # gitignore ルートからの相対パスを計算
            try:
                current_dir_rel_to_gitignore = current_dir_abs.relative_to(self.gitignore_root)
            except ValueError:
                # gitignore ルートの外にある場合はスキップ
                current_dir_rel_to_gitignore = Path(".")

            # サブディレクトリをフィルタリング（.gitignore ルールに基づく）
            # os.walk は dirnames を in-place で変更することで、
            # 除外されたディレクトリに入らないようにできる
            dirnames[:] = [
                dirname for dirname in dirnames
                if not self._should_ignore_directory(current_dir_rel_to_gitignore / dirname)
            ]

            # ファイルを処理
            for name in filenames:
                full_path = Path(dirpath) / name

                # gitignore ルートからの相対パスを計算
                try:
                    rel_path_to_gitignore = full_path.relative_to(self.gitignore_root)
                    if self.spec.match_file(str(rel_path_to_gitignore).replace('\\', '/')):
                        continue
                except ValueError:
                    # gitignore ルートの外にある場合は除外しない
                    pass

                yield full_path

    def _should_ignore_directory(self, rel_dir_path: Path) -> bool:
        """ディレクトリが .gitignore ルールによって除外されるべきかチェック。

        Args:
            rel_dir_path: ルートからの相対ディレクトリパス

        Returns:
            True if ディレクトリを除外すべき場合
        """
        # パスを正規化（Windowsの場合のバックスラッシュ対応）
        dir_path_str = str(rel_dir_path).replace('\\', '/')

        # 空のパス（ルートディレクトリ）は除外しない
        if not dir_path_str or dir_path_str == '.':
            return False

        # 複数のパターンでチェック
        patterns_to_check = [
            dir_path_str,                    # そのまま
            dir_path_str + '/',              # 末尾にスラッシュ
            dir_path_str.split('/')[-1],     # ディレクトリ名のみ
            dir_path_str.split('/')[-1] + '/', # ディレクトリ名のみ + スラッシュ
        ]

        # 各パターンをチェック
        for pattern in patterns_to_check:
            if self.spec.match_file(pattern):
                return True

        # 親ディレクトリのパターンもチェック（再帰的な除外）
        # 例: __pycache__/ が除外されている場合、auth/__pycache__/ も除外
        parts = dir_path_str.split('/')
        for i in range(len(parts)):
            # 各レベルでのディレクトリ名をチェック
            dir_name = parts[i]
            if self.spec.match_file(dir_name + '/') or self.spec.match_file(dir_name):
                return True

        return False

    def _file_hash(self, path: Path, algorithm: str = DEFAULT_HASH_ALG) -> str:
        hasher = hashlib.new(algorithm)
        with path.open("rb") as fp:
            for chunk in iter(lambda: fp.read(8192), b""):  # 8 KB
                hasher.update(chunk)
        return hasher.hexdigest()


