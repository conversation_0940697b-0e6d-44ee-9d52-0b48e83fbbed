# ==============================================================
"""ディレクトリ走査 & .gitignore 対応ハッシュ計算。"""

from __future__ import annotations
import hashlib
import os
from pathlib import Path
from typing import Dict, Iterable, Set

import pathspec  # type: ignore[import]

DEFAULT_HASH_ALG = "sha256"
IGNORE_FILE_NAME = ".gitignore"

# コードファイルとして認識する拡張子
CODE_EXTENSIONS = {
    # プログラミング言語
    '.py', '.pyx', '.pyi',  # Python
    '.js', '.jsx', '.ts', '.tsx', '.mjs', '.cjs',  # JavaScript/TypeScript
    '.java', '.kt', '.scala', '.groovy',  # JVM言語
    '.c', '.cpp', '.cc', '.cxx', '.h', '.hpp', '.hxx',  # C/C++
    '.cs', '.vb', '.fs',  # .NET
    '.go', '.rs', '.swift', '.dart',  # 現代言語
    '.php', '.rb', '.pl', '.pm', '.r',  # スクリプト言語
    '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat', '.cmd',  # シェル
    '.sql', '.plsql',  # データベース
    '.lua', '.vim', '.el',  # その他

    # マークアップ・設定ファイル
    '.html', '.htm', '.xml', '.xhtml', '.svg',  # マークアップ
    '.css', '.scss', '.sass', '.less', '.styl',  # スタイル
    '.json', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf',  # 設定
    '.md', '.rst', '.txt', '.tex',  # ドキュメント
    '.dockerfile', '.makefile', '.cmake',  # ビルド

    # テンプレート・ビュー
    '.jsp', '.jspx', '.ftl', '.vm', '.twig', '.blade.php',  # Java/PHP テンプレート
    '.erb', '.haml', '.slim',  # Ruby テンプレート
    '.vue', '.svelte',  # フロントエンド
    '.hbs', '.handlebars', '.mustache',  # テンプレートエンジン
}

# 除外する拡張子（二進制文件和非代码文件）
BINARY_EXTENSIONS = {
    # Java関連バイナリ
    '.jar', '.war', '.ear', '.class', '.jmod',

    # 実行ファイル・ライブラリ
    '.exe', '.dll', '.so', '.dylib', '.a', '.lib', '.o', '.obj',

    # アーカイブ・圧縮
    '.zip', '.tar', '.gz', '.bz2', '.xz', '.7z', '.rar',

    # 画像・メディア
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico', '.svg', '.webp',
    '.mp3', '.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm',
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',

    # データベース・ログ
    '.db', '.sqlite', '.sqlite3', '.mdb', '.accdb',
    '.log', '.tmp', '.temp', '.cache', '.pid',

    # その他
    '.pyc', '.pyo', '.pyd', '__pycache__',
    '.node_modules', '.git', '.svn', '.hg',
}


class DirectoryScanner:
    """対象ディレクトリを走査し、ファイルハッシュ辞書を返すクラス。"""

    def __init__(self, root: str | Path, filter_code_files: bool = True):
        """
        Args:
            root: スキャン対象のルートディレクトリ
            filter_code_files: Trueの場合、コードファイルのみをスキャン（デフォルト）
        """
        self.root = Path(root).resolve()
        self.filter_code_files = filter_code_files
        self.spec, self.gitignore_root = self._load_gitignore()

    # ────────────────────────────────────────
    # パブリック API
    # ────────────────────────────────────────
    def scan(self) -> Dict[str, str]:
        """root 配下ファイルを走査し {path: hash} を返却。"""
        result: Dict[str, str] = {}
        for f in self._iter_files():
            result[str(f)] = self._file_hash(f)
        return result

    # ────────────────────────────────────────
    # 内部処理
    # ────────────────────────────────────────
    def _is_code_file(self, file_path: Path) -> bool:
        """ファイルがコードファイルかどうかを判定。

        Args:
            file_path: 判定対象のファイルパス

        Returns:
            True if コードファイルの場合
        """
        if not self.filter_code_files:
            return True

        # 拡張子を取得（小文字に変換）
        ext = file_path.suffix.lower()

        # 二進制文件や除外対象の拡張子をチェック
        if ext in BINARY_EXTENSIONS:
            return False

        # 特定のファイル名パターンをチェック
        filename = file_path.name.lower()

        # 除外するファイル名パターン
        exclude_patterns = {
            'makefile', 'dockerfile', 'readme', 'license', 'changelog',
            'authors', 'contributors', 'copying', 'install', 'news'
        }

        # 拡張子なしの特別なファイル（Makefile等）をチェック
        if not ext and filename in exclude_patterns:
            # ただし、Makefile、Dockerfileなどはコードファイルとして扱う
            if filename in {'makefile', 'dockerfile'}:
                return True
            return False

        # コードファイルの拡張子をチェック
        if ext in CODE_EXTENSIONS:
            return True

        # 拡張子がない場合、内容をチェックして判定
        if not ext:
            return self._is_text_file(file_path)

        return False

    def _is_text_file(self, file_path: Path) -> bool:
        """ファイルがテキストファイルかどうかを判定（バイナリファイル検出）。

        Args:
            file_path: 判定対象のファイルパス

        Returns:
            True if テキストファイルの場合
        """
        try:
            # ファイルサイズが大きすぎる場合はスキップ
            if file_path.stat().st_size > 10 * 1024 * 1024:  # 10MB
                return False

            # 最初の1024バイトを読んでバイナリかどうか判定
            with file_path.open('rb') as f:
                chunk = f.read(1024)

            # 空ファイルはテキストファイルとして扱う
            if not chunk:
                return True

            # NULL文字が含まれている場合はバイナリファイル
            if b'\x00' in chunk:
                return False

            # 制御文字の割合をチェック
            text_chars = bytearray({7, 8, 9, 10, 12, 13, 27} | set(range(0x20, 0x100)) - {0x7f})
            non_text_count = sum(1 for byte in chunk if byte not in text_chars)

            # 30%以上が制御文字の場合はバイナリファイル
            if len(chunk) > 0 and (non_text_count / len(chunk)) > 0.30:
                return False

            return True

        except (OSError, IOError, UnicodeDecodeError):
            return False
    def _load_gitignore(self):
        """プロジェクトルートの .gitignore ファイルを読み込む。

        Returns:
            tuple: (pathspec.PathSpec, gitignore_root_path)
        """
        # まず、スキャン対象ディレクトリ内の .gitignore をチェック
        ignore_path = self.root / IGNORE_FILE_NAME
        if ignore_path.exists():
            with ignore_path.open("r", encoding="utf-8") as fp:
                spec = pathspec.PathSpec.from_lines("gitwildmatch", fp)
            return spec, self.root

        # スキャン対象ディレクトリにない場合、親ディレクトリを遡って探す
        current_path = self.root.parent
        while current_path != current_path.parent:  # ルートディレクトリに到達するまで
            gitignore_path = current_path / IGNORE_FILE_NAME
            if gitignore_path.exists():
                with gitignore_path.open("r", encoding="utf-8") as fp:
                    spec = pathspec.PathSpec.from_lines("gitwildmatch", fp)
                return spec, current_path
            current_path = current_path.parent

        return pathspec.PathSpec([]), self.root

    def _iter_files(self) -> Iterable[Path]:
        """ファイルを再帰的に走査し、.gitignore ルールに従ってフィルタリング。

        ディレクトリレベルでの除外も適切に処理する。
        """
        for dirpath, dirnames, filenames in os.walk(self.root):
            # 現在のディレクトリの相対パス（gitignore ルートからの相対パス）
            current_dir_abs = Path(dirpath)

            # gitignore ルートからの相対パスを計算
            try:
                current_dir_rel_to_gitignore = current_dir_abs.relative_to(self.gitignore_root)
            except ValueError:
                # gitignore ルートの外にある場合はスキップ
                current_dir_rel_to_gitignore = Path(".")

            # サブディレクトリをフィルタリング（.gitignore ルールに基づく）
            # os.walk は dirnames を in-place で変更することで、
            # 除外されたディレクトリに入らないようにできる
            dirnames[:] = [
                dirname for dirname in dirnames
                if not self._should_ignore_directory(current_dir_rel_to_gitignore / dirname)
            ]

            # ファイルを処理
            for name in filenames:
                full_path = Path(dirpath) / name

                # gitignore ルートからの相対パスを計算
                try:
                    rel_path_to_gitignore = full_path.relative_to(self.gitignore_root)
                    if self.spec.match_file(str(rel_path_to_gitignore).replace('\\', '/')):
                        continue
                except ValueError:
                    # gitignore ルートの外にある場合は除外しない
                    pass

                # コードファイルフィルタリング
                if not self._is_code_file(full_path):
                    continue

                yield full_path

    def _should_ignore_directory(self, rel_dir_path: Path) -> bool:
        """ディレクトリが .gitignore ルールによって除外されるべきかチェック。

        Args:
            rel_dir_path: ルートからの相対ディレクトリパス

        Returns:
            True if ディレクトリを除外すべき場合
        """
        # パスを正規化（Windowsの場合のバックスラッシュ対応）
        dir_path_str = str(rel_dir_path).replace('\\', '/')

        # 空のパス（ルートディレクトリ）は除外しない
        if not dir_path_str or dir_path_str == '.':
            return False

        # 複数のパターンでチェック
        patterns_to_check = [
            dir_path_str,                    # そのまま
            dir_path_str + '/',              # 末尾にスラッシュ
            dir_path_str.split('/')[-1],     # ディレクトリ名のみ
            dir_path_str.split('/')[-1] + '/', # ディレクトリ名のみ + スラッシュ
        ]

        # 各パターンをチェック
        for pattern in patterns_to_check:
            if self.spec.match_file(pattern):
                return True

        # 親ディレクトリのパターンもチェック（再帰的な除外）
        # 例: __pycache__/ が除外されている場合、auth/__pycache__/ も除外
        parts = dir_path_str.split('/')
        for i in range(len(parts)):
            # 各レベルでのディレクトリ名をチェック
            dir_name = parts[i]
            if self.spec.match_file(dir_name + '/') or self.spec.match_file(dir_name):
                return True

        return False

    def _file_hash(self, path: Path, algorithm: str = DEFAULT_HASH_ALG) -> str:
        hasher = hashlib.new(algorithm)
        with path.open("rb") as fp:
            for chunk in iter(lambda: fp.read(8192), b""):  # 8 KB
                hasher.update(chunk)
        return hasher.hexdigest()


