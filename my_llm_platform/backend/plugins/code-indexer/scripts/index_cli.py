# ==============================================================
"""CLI エントリポイント。

Usage::
    poetry run python scripts/index_cli.py init  --path /repo  --index faiss.idx
    poetry run python scripts/index_cli.py rebuild --path /repo  --index faiss.idx
    poetry run python scripts/index_cli.py watch --path /repo  --index faiss.idx
    poetry run python scripts/index_cli.py query --index faiss.idx "クエリ文"
"""

from __future__ import annotations
import argparse
import sys
from pathlib import Path
from typing import List

import numpy as np
import faiss  # type: ignore[import]

# Add the parent directory to sys.path to enable absolute imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from indexer.index_updater import CodeIndexer
from indexer.embedder import Embedder


def _init(args):
    print(f"🚀 インデックス作成開始...")
    print(f"📁 対象パス: {args.path}")
    print(f"💾 インデックスファイル: {args.index}")

    try:
        ci = CodeIndexer(args.path, args.index)
        print("📊 CodeIndexer初期化完了")

        ci.full_build()
        print("✅ Full indexing completed.")

        # 統計情報を表示
        stats = ci.get_index_stats()
        print(f"📈 統計情報:")
        print(f"  - 総ベクトル数: {stats['total_vectors']}")
        print(f"  - 総ファイル数: {stats['total_files']}")
        print(f"  - 総チャンク数: {stats['total_chunks']}")

    except Exception as e:
        print(f"❌ エラーが発生しました: {e}")
        import traceback
        traceback.print_exc()
        raise


def _watch(args):
    ci = CodeIndexer(args.path, args.index)
    ci.watch()


def _rebuild(args):
    """既存のインデックスを完全に再構築（安全な方法）"""
    print(f"🔄 インデックス再構築開始...")
    print(f"📁 対象パス: {args.path}")
    print(f"💾 インデックスファイル: {args.index}")

    try:
        # 安全な再構築：既存ファイルを削除してから新規作成
        index_file = args.index
        state_file = index_file.with_suffix(".json")

        print("🗑️  既存のインデックスファイルを削除中...")
        if index_file.exists():
            index_file.unlink()
            print(f"   削除: {index_file}")

        if state_file.exists():
            state_file.unlink()
            print(f"   削除: {state_file}")

        print("🚀 新しいインデックスを作成中...")
        ci = CodeIndexer(args.path, args.index)
        print("📊 CodeIndexer初期化完了")

        ci.full_build()
        print("✅ Index rebuild completed.")

        # 統計情報を表示
        stats = ci.get_index_stats()
        print(f"📈 統計情報:")
        print(f"  - 総ベクトル数: {stats['total_vectors']}")
        print(f"  - 総ファイル数: {stats['total_files']}")
        print(f"  - 総チャンク数: {stats['total_chunks']}")

    except Exception as e:
        print(f"❌ エラーが発生しました: {e}")
        import traceback
        traceback.print_exc()
        raise


def _query(args):
    embedder = Embedder()
    qv = embedder.embed([args.query])
    index = faiss.read_index(str(args.index))
    D, I = index.search(qv, k=5)  # 近傍 5 件

    # 状態ファイルを読み込んで逆マッピングを作成
    state_file = args.index.with_suffix(".json")
    if state_file.exists():
        import json
        state = json.loads(state_file.read_text("utf-8"))
        id_map = state.get("id_map", {})

        # 逆マッピングを作成: chunk_id -> (file_path, chunk_index)
        reverse_map = {}
        for file_path, chunk_ids in id_map.items():
            for chunk_index, chunk_id in enumerate(chunk_ids):
                reverse_map[chunk_id] = (file_path, chunk_index)

        print(f"🔍 検索クエリ: '{args.query}'")
        print("=" * 60)

        for rank, (chunk_id, distance) in enumerate(zip(I[0], D[0]), 1):
            if chunk_id in reverse_map:
                file_path, chunk_index = reverse_map[chunk_id]
                # ファイル名のみを表示（パスが長い場合）
                file_name = Path(file_path).name
                print(f"#{rank} [{distance:.4f}] {file_name} (chunk {chunk_index})")
                print(f"    📁 {file_path}")

                # 実際のチャンク内容を表示（可能であれば）
                try:
                    if Path(file_path).exists():
                        from indexer.chunker import CodeChunker
                        chunker = CodeChunker()
                        text = Path(file_path).read_text(encoding="utf-8", errors="ignore")
                        chunks = chunker.split(text, file_path)
                        if chunk_index < len(chunks):
                            chunk_content = chunks[chunk_index][:200] + "..." if len(chunks[chunk_index]) > 200 else chunks[chunk_index]
                            print(f"    📝 {chunk_content}")
                except Exception as e:
                    print(f"    ❌ チャンク内容の読み込みエラー: {e}")
                print()
            else:
                print(f"#{rank} [Unknown] Chunk ID: {chunk_id} (距離: {distance:.4f})")
                print()
    else:
        print("❌ 状態ファイルが見つかりません。インデックスが正しく作成されていない可能性があります。")
        print("🔍 Raw results →", I[0])


def main(argv: List[str] | None = None):
    parser = argparse.ArgumentParser()
    sub = parser.add_subparsers(dest="cmd", required=True)

    p_init = sub.add_parser("init", help="新規インデックス作成")
    p_init.add_argument("--path", required=True, type=Path, help="スキャン対象のディレクトリ")
    p_init.add_argument("--index", required=True, type=Path, help="インデックスファイルのパス")
    p_init.set_defaults(func=_init)

    p_rebuild = sub.add_parser("rebuild", help="既存インデックスの完全再構築")
    p_rebuild.add_argument("--path", required=True, type=Path, help="スキャン対象のディレクトリ")
    p_rebuild.add_argument("--index", required=True, type=Path, help="インデックスファイルのパス")
    p_rebuild.set_defaults(func=_rebuild)

    p_watch = sub.add_parser("watch", help="ファイル変更の常時監視")
    p_watch.add_argument("--path", required=True, type=Path, help="監視対象のディレクトリ")
    p_watch.add_argument("--index", required=True, type=Path, help="インデックスファイルのパス")
    p_watch.set_defaults(func=_watch)

    p_query = sub.add_parser("query", help="インデックスから検索")
    p_query.add_argument("--index", required=True, type=Path, help="インデックスファイルのパス")
    p_query.add_argument("query", help="検索クエリ")
    p_query.set_defaults(func=_query)

    args = parser.parse_args(argv)
    args.func(args)


if __name__ == "__main__":
    main()


