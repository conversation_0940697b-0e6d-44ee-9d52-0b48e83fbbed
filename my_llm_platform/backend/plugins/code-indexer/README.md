# ==============================================================
"""# code-indexer

Cursor 風ローカルコードベース索引サービス。

## Quick Start
```bash
# 依存インストール
poetry install

# フルインデックス作成
poetry run python scripts/index_cli.py init  --path /mnt/d/workspase/ai_pj_demo/pj_demo \
                                          --index ./faissdb/faiss.index
# 常時監視 (Ctrl+C で停止)
poetry run python scripts/index_cli.py watch --path /mnt/d/workspase/ai_pj_demo/pj_demo \
                                            --index ./faissdb/faiss.index
# 検索 (ベクトル近傍 ID を取得)
poetry run python scripts/index_cli.py query --index ./faissdb/faiss.index "エントリーポイントは？"
```

## ライセンス
MIT
"""


### README.md

---
# Code‑Indexer
_Local codebase embedding & incremental indexing service_

## ✨ Features
| 機能 / Feature | 説明 / Description |
|----------------|--------------------|
| **Incremental Merkle Scan** | Detects file changes with SHA‑256 Merkle tree, only re‑indexes deltas |
| **Chunk‑Aware Embedding** | Re‑uses `rag_service.DocumentLoader` so chunking rules stay identical |
| **Faiss Hybrid Index** | Automatically picks `IndexFlatL2` (<100k vecs) or `IndexIVFFlat+OPQ` (>100k) |
| **Watchdog Realtime Update** | File change events trigger `RAGService.add_document()` within <1 s |
| **CLI & Service Mode** | `code-indexer {init,update,query,info}` and systemd / NSSM service wrappers |

## 🏗️ Installation
```bash
# 1. clone & install
git clone https://example.com/code-indexer.git
cd code-indexer
poetry install

# 2. configure env (zsh/bash)
export INDEX_ROOT=$HOME/your_repo
export FAISS_INDEX=$HOME/.cache/code_index.faiss
export EMBEDDING_MODEL=thenlper/gte-base  # or keep rag_service default
```

## 🚀 Quick Start
```bash
# 初期索引
poetry run code-indexer init --path $INDEX_ROOT

# バックグラウンド監視 (Linux user service)
cp deploy/code-indexer.service ~/.config/systemd/user/
systemctl --user daemon-reload
systemctl --user enable --now code-indexer

# 查询
poetry run code-indexer query "ユーザー認証ロジックは？"
```

## 🔧 Configuration Options
| ENV / CLI | Default | Note |
|-----------|---------|------|
| `INDEX_ROOT` | `pwd` | target repository path |
| `FAISS_INDEX` | `.faiss` under repo | absolute path recommended for services |
| `EMBEDDING_MODEL` | `thenlper/gte-base` | must equal rag_service VectorStore model |
| `MAX_WORKERS` | `8` | file I/O thread pool size |

## 🩹 Troubleshooting
* **索引过慢** → increase `MAX_WORKERS`, exclude large binaries via `.gitignore`
* **维度不符** → ensure both services use same `SentenceTransformer` model
* **GPU Faiss** → `poetry add faiss-gpu` then set `FAISS_USE_GPU=1`

## 📜 License
MIT

---

_最終更新 / Last Updated: 2025‑06‑02_



------------------改善用-------------------------
# 検索 (ベクトル近傍 ID を取得)
poetry run python scripts/index_cli.py query --index ./faissdb/faiss.index "提示词替换"

-------提示词替换---------
下記の変更要求に基づいて、プログラムソースをカ所を洗い出し

変更背景・目的：
-化学品配送における安全性向上
-危険物取扱いの法令遵守強化
-配送効率の最適化

対象機能：
-ライン変更機能

変更仕様：
1.追加項目
　1)危険物等級区分（セレクトボックス）【必須】
   -値：第一級、第二級、第三級、第四級
   -入力検証：必須チェック、コード値チェック、同一車両混載制限チェック（第一級と第四級は混載不可）
　2)温度管理区分（ラジオボタン）【必須】
   -値：常温、定温、冷蔵、冷凍
   -関連項目：温度上限、温度下限（数値入力欄）
   -表示制御：温度管理区分が「冷蔵」「冷凍」の場合のみ、温度範囲欄を表示
2.削除項目
　-割増初期化要否フラグ（画面上の入力削除）

以下の変更要求に基づいて、pj_demo プロジェクトのソースコードから修正が必要な箇所を特定し、具体的な変更計画を作成してください。

**変更背景・目的：**
- 化学品配送における安全性向上
- 危険物取扱いの法令遵守強化  
- 配送効率の最適化

**対象機能：**
- ライン変更機能（配送ライン管理システム）

**変更仕様：**

**1. 追加項目**
   **1) 危険物等級区分（セレクトボックス）【必須】**
   - 選択値：第一級、第二級、第三級、第四級
   - 入力検証要件：
     - 必須チェック
     - コード値チェック
     - 同一車両混載制限チェック（第一級と第四級は混載不可）
   
   **2) 温度管理区分（ラジオボタン）【必須】**
   - 選択値：常温、定温、冷蔵、冷凍
   - 関連項目：温度上限、温度下限（数値入力欄）
   - 表示制御：温度管理区分が「冷蔵」「冷凍」選択時のみ温度範囲欄を表示

**2. 削除項目**
   - 割増初期化要否フラグ（画面上の入力項目を削除）

**求める成果物：**
1. 影響を受けるソースファイルの一覧
2. 各ファイルでの具体的な変更箇所の特定
3. データベーススキーマ変更の必要性
4. フロントエンド・バックエンド両方の変更点
5. テストケースの更新が必要な箇所
6. 変更の優先順位と実装手順の提案
