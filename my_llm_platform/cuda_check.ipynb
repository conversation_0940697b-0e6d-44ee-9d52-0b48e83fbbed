import re

print("\n💡 最終値:")
text = "まず、括弧内の式を計算します: (3 + 5) = 8。次に、8 を 12 で掛けます: 8 * 12 = 96。したがって、(3 + 5) * 12 の値は 96 です。"
text = re.sub(r"\*\*", "", text)
matches = re.findall(r"\d+(?:\.\d+)?(?=\s*です)", text)
print(matches[0] if matches else "None!")

import torch
print(torch.__version__)
print(torch.cuda.is_available())
print(torch.version.cuda)
print(torch.cuda.get_device_name(0) if torch.cuda.is_available() else "No GPU")


import requests
url = "http://localhost:8001/v1/completions"

payload = {
    "model": "facebook/opt-125m",
    "prompt": "你好",
    "max_tokens": 50
}

headers = {"Content-Type": "application/json"}

response = requests.post(url, json=payload, headers=headers)

print(response.json())


import openai

client = openai.OpenAI(
    base_url="http://localhost:8000/v1",
    api_key="EMPTY"
)
model_name = "facebook/opt-125m" # "facebook/opt-125m" "deepseek-r1:7b"
response = client.chat.completions.create(
    model=model_name,
    messages=[
        {"role": "system", "content": "あなたは親切なアシスタントです。"},
        {"role": "user", "content": "你好"}
    ],
    max_tokens=500
)

print(response.choices[0].message.content)